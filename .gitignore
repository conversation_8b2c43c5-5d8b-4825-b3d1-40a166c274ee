# Python-generated files
__pycache__/
*.py[oc]
build/
dist/
wheels/
*.egg-info

# Virtual environments
.venv/

# vscode
.vscode/

# Cursor
.cursor/

# Claude
.claude/
CLAUDE.md

# idea
.idea/

# GEMINI
.gemini/
gemini.md
.env

# Configuration files
config/*
!config/.gitkeep

# Private information
local_data/*
!local_data/.gitkeep
!local_data/vector_data/
local_data/vector_data/*
!local_data/vector_data/.gitkeep
knowledge_documents/*
!knowledge_documents/.gitkeep
api.txt

# models
models/*
!models/.gitkeep
!models/deepseek_v3_tokenizer/

# MarkDown files
!knowledge_documents/TAPD API 文档/
!knowledge_documents/DeepSeek API 文档/
!knowledge_documents/SiliconFlow API 文档/
!knowledge_documents/DeepSeek API 环境变量配置指南.md
!knowledge_documents/API兼容性增强功能使用手册.md
!knowledge_documents/get_tapd_data工具使用手册.md
!knowledge_documents/TAPD数据预处理工具使用手册.md
!knowledge_documents/TAPD词频分析工具使用手册.md
!knowledge_documents/TAPD数据向量化功能使用手册.md
!knowledge_documents/测试用例规则自定义功能使用手册.md
!knowledge_documents/历史需求知识库使用指南.md
!knowledge_documents/AI测试用例评估器操作手册.md
