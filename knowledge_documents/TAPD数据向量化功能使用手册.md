# TAPD数据向量化功能使用手册

## 功能概述

TAPD数据向量化功能是专为解决大批量TAPD数据处理而设计的智能搜索系统。
当需求或缺陷数据超过200条时，传统方式会因tokens超限而无法让AI正确分析，
而向量化功能通过语义搜索技术完美解决了这个问题。

### 🎯 解决核心痛点

- **突破Token限制**：处理200+条数据不再受限
- **智能语义搜索**：支持自然语言查询，无需精确关键词
- **秒级响应**：优化缓存机制，搜索延迟 < 1秒
- **精准匹配**：基于语义相似度，而非简单关键词匹配

### 🚀 技术特性

- **高性能向量引擎**：基于FAISS构建，支持大规模数据
- **中文优化模型**：使用paraphrase-MiniLM-L6-v2，专门优化中文语义理解
- **智能分片策略**：可配置分片大小，平衡精度与效率
- **持久化存储**：向量数据本地存储，支持增量更新

## 快速开始

### 第一步：环境检查

确保已完成基础环境搭建（参考README.md的迁移步骤）：

```bash
# 检查Python版本
python --version  # 应显示 Python 3.10.x

# 检查依赖
uv --version

# 进入项目目录
cd D:\MCPAgentRE  # 根据实际路径调整
```

### 第二步：快速启动

运行快速启动脚本：

```bash
uv run test\vector_quick_start.py
```

预期输出：

```bash
🚀 TAPD向量化功能快速启动
==================================================
✅ 数据文件检查通过
📦 向量数据库不存在，开始初始化...
✅ 向量化完成!
   • 总分片数: 3
   • 总条目数: 19
   • 向量维度: 384

🔍 演示搜索功能...
🔎 搜索: '订单相关功能'
   找到 2 个结果:
   1. [story] 【示例】订单撤销 (相关度: 0.498)
   2. [bug] 订单支付页面的部分数据被UI遮挡 (相关度: 0.509)
🎉 快速启动完成!
```

### 第三步：集成到Claude Desktop

按照README.md中的"如何将项目连接到AI客户端"部分完成配置。

## MCP工具详解

项目提供3个核心MCP工具，可在Claude Desktop中直接调用：

### 1. vectorize_data - 数据向量化

**功能**：将TAPD数据转换为向量表示，建立搜索索引

**语法**：

```text
vectorize_data(chunk_size=10)
```

**参数说明**：

- `chunk_size`：分片大小，每个分片包含的条目数
  - 推荐值：10-20（平衡精度与效率）
  - 较小值：搜索更精准，但分片更多
  - 较大值：减少分片数量，但可能降低搜索精度

**使用场景**：

- ✅ 首次使用向量化功能
- ✅ TAPD数据有更新时
- ✅ 需要调整分片策略时

**使用示例**：

```text
请使用vectorize_data工具对当前TAPD数据进行向量化，使用默认分片大小
```

### 2. search_data - 智能搜索

**功能**：基于语义相似度搜索相关的需求和缺陷

**语法**：

```text
search_data(query, top_k=5)
```

**参数说明**：

- `query`：搜索查询，支持自然语言描述
- `top_k`：返回结果数量，建议3-10个

**查询技巧**：

- 🎯 **功能描述**："订单撤销功能"、"用户登录模块"
- 🐛 **问题类型**："页面异常"、"数据显示错误"
- 📊 **属性过滤**："高优先级"、"已完成状态"
- 🔍 **组合查询**："商品评价相关的缺陷"

**使用示例**：

```text
使用search_data工具搜索"订单相关的功能需求"，返回前3个最相关的结果
```

### 3. get_vector_info - 状态查询

**功能**：获取向量数据库的状态和统计信息

**语法**：

```text
get_vector_info()
```

**返回信息**：

- 数据库状态（ready/not_found/error）
- 总分片数和条目数
- 需求/缺陷分片分布
- 向量维度和存储路径

**使用示例**：

```text
请使用get_vector_info工具检查向量数据库的当前状态
```

## 实际应用场景

### 场景1：需求分析

**目标**：分析订单相关的所有需求

```text
1. 使用search_data搜索"订单"
2. 获得相关需求列表
3. 进一步搜索"订单 + 支付"细化结果
```

### 场景2：缺陷排查

**目标**：查找UI相关的所有缺陷

```text
1. 使用search_data搜索"页面异常"
2. 搜索"UI遮挡"找到具体问题
3. 分析相关缺陷的优先级分布
```

### 场景3：功能梳理

**目标**：梳理某个模块的完整功能

```text
1. 搜索"商品评价"获得功能概览
2. 搜索"评价 + 缺陷"查看相关问题
3. 形成完整的功能现状报告
```

## 高级使用技巧

### 1. 优化搜索查询

- **使用核心关键词**：避免冗长描述，突出核心概念
- **组合查询**：使用"功能名 + 属性"的模式
- **渐进式搜索**：从宽泛到具体，逐步细化

### 2. 分片策略选择

```text
小项目（<50条）：chunk_size=5-8
中等项目（50-200条）：chunk_size=10-15  ✅ 推荐
大型项目（>200条）：chunk_size=15-25
```

### 3. 结果解读技巧

- **相关度分数**：>0.6为高度相关，0.4-0.6为中度相关
- **结果类型**：story（需求）vs bug（缺陷）
- **条目数量**：分片包含的原始条目数

## 性能优化建议

### 1. 提升搜索精度

- 定期重新向量化数据（数据更新后）
- 根据项目特点调整分片大小
- 使用具体的业务术语进行搜索

### 2. 加快响应速度

- 避免频繁重启向量化进程
- 合理设置top_k值（通常5-10个足够）
- 利用模型缓存机制

### 3. 内存使用优化

- 大型项目考虑分批向量化
- 定期清理不需要的向量文件
- 监控系统资源使用情况

## 故障排除

### 常见问题1：向量化失败

**现象**：`vectorize_data`工具返回错误状态
**原因**：数据文件不存在或格式错误
**解决**：

```bash
# 检查数据文件
ls local_data/msg_from_fetcher.json

# 重新获取TAPD数据
uv run tapd_data_fetcher.py
```

### 常见问题2：搜索无结果

**现象**：`search_data`返回空结果或相关度很低
**原因**：查询词与数据内容差异较大
**解决**：

- 尝试使用更通用的关键词
- 检查数据是否已正确向量化
- 调整搜索关键词的表达方式

### 常见问题3：性能缓慢

**现象**：搜索响应时间过长
**原因**：模型未缓存或系统资源不足
**解决**：

```bash
# 运行预热脚本
uv run test\vector_quick_start.py

# 检查系统资源
# 确保有足够的内存和CPU资源
```

## 最佳实践总结

### ✅ 推荐做法

1. **初次使用**：先运行`uv run test\vector_quick_start.py`进行环境验证
2. **数据更新**：TAPD数据变更后及时重新向量化
3. **查询优化**：使用业务相关的专业术语
4. **结果验证**：关注相关度分数，筛选高质量结果

### ❌ 避免事项

1. **频繁向量化**：避免不必要的重复向量化操作
2. **过长查询**：避免使用过于复杂的查询语句
3. **忽略分数**：不要忽略相关度分数的参考价值
4. **盲目增大top_k**：过多结果可能降低分析效率

## 技术架构说明

### 向量化模型

- **模型**：`paraphrase-MiniLM-L6-v2`
- **维度**：384维向量
- **语言**：中英文兼容，中文优化
- **大小**：约90MB，首次下载后本地缓存
- **🆕 本地缓存**：模型自动存储在项目`models`目录，无需VPN访问

### 📁 模型存储结构

```text
models/
└─ models--sentence-transformers--paraphrase-MiniLM-L6-v2/
   ├─ blobs/                    # 模型文件存储
   ├─ refs/                     # 引用信息
   └─ snapshots/               # 模型快照
      └─ [版本号]/             # 具体版本目录
         ├─ config.json        # 模型配置
         ├─ pytorch_model.bin  # 模型权重
         └─ tokenizer.json     # 分词器配置
```

### 🔄 智能模型加载机制

1. **优先本地**：首先检查项目`models`目录中的缓存模型
2. **自动下载**：本地不存在时自动下载到项目目录（仅首次需要VPN）
3. **版本管理**：自动选择最新版本的模型快照
4. **全局缓存**：运行时在内存中缓存，避免重复加载

### 数据存储结构

```text
local_data/
├─ data_vector.index        # FAISS向量索引文件
├─ data_vector.metadata.pkl # 元数据（分片信息、原始数据）
└─ data_vector.config.json  # 配置信息（模型名称、创建时间等）
```

### 搜索流程

1. **查询预处理**：将查询文本转换为向量
2. **相似度计算**：使用余弦相似度匹配
3. **结果排序**：按相关度分数排序
4. **数据重构**：从元数据中恢复原始TAPD条目

## 总结

TAPD数据向量化功能为大批量数据处理提供了完美解决方案。通过智能语义搜索，用户可以快速定位相关需求和缺陷，大幅提升工作效率。

**核心价值**：

- 🎯 **解决根本问题**：突破Token限制，处理大规模数据
- 🚀 **提升工作效率**：秒级搜索，精准匹配
- 🧠 **智能化体验**：自然语言查询，语义理解

建议用户从基础功能开始，逐步探索高级特性，充分发挥向量化搜索的强大能力。
