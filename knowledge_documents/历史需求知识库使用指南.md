# 历史需求知识库使用指南

## 功能说明

历史需求知识库提供了一个轻量级的解决方案，用于增强TAPD数据，让现有的搜索功能返回更有价值的信息。

## 核心特点

- **极简设计**: 不创建新数据库，只在现有TAPD数据上增加知识库信息
- **复用现有功能**: 利用项目现有的`search_data()`工具进行语义搜索
- **自动备份**: 增强数据前自动备份原文件
- **测试用例关联**: 可选择性地关联测试用例Excel文件

## 使用步骤

### 1. 获取TAPD数据

```bash
uv run tapd_data_fetcher.py
```

### 2. 增强数据（添加知识库信息）

使用MCP工具：

```python
enhance_tapd_with_knowledge(
    tapd_file="local_data/msg_from_fetcher.json",
    testcase_file="local_data/TestCase_20250717141033-32202633.xlsx"  # 可选
)
```

### 3. 搜索相似需求

使用现有的搜索工具：

```python
search_data(query="用户登录功能", top_k=5)
```

搜索结果将包含：

- 原有的需求信息
- 功能类型分类
- 测试用例建议
- 相关关键词

## 数据结构

增强后的每个需求会添加`kb_info`字段：

```json
{
  "id": "story_12345",
  "name": "用户登录功能",
  "description": "...",
  "kb_info": {
    "feature_type": "用户认证",
    "test_case_suggestions": [
      "验证用户名密码登录",
      "验证登录失败提示",
      "验证记住密码功能"
    ],
    "similar_keywords": ["登录", "验证", "密码", "用户"]
  }
}
```

## 测试用例分类

系统会自动将测试用例分类为：

- 用户认证
- 搜索功能
- 文件操作
- 交易流程
- 权限管理
- 通用功能

## 测试方法

运行测试脚本：

```bash
uv run test\test_knowledge_base.py
uv run test\test_knowledge_base_modification.py
```

## 优势

1. **轻量级**: 不增加额外的数据库或复杂的存储结构
2. **兼容性**: 完全兼容现有的搜索和分析工具
3. **实用性**: 直接为搜索结果添加测试用例建议
4. **可扩展**: 可以轻松增加新的功能类型分类规则

## 注意事项

- 增强数据前会自动备份原文件为`.backup.json`
- 测试用例文件是可选的，没有也可以运行
- 增强操作会覆盖原有的TAPD数据文件
- 建议定期重新增强数据以保持最新状态
