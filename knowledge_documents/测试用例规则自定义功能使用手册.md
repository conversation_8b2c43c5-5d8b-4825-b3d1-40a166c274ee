# 测试用例评估规则自定义功能使用手册

## 功能概述

该功能允许用户灵活配置测试用例AI评估的规则参数，包括：

1. **标题长度限制** - 控制用例标题的最大字符数
2. **步骤数上限** - 控制测试步骤的最大数量
3. **三类优先级占比** - 控制P0、P1、P2优先级的合理占比范围

## 文件说明

- `mcp_tools\test_case_rules_customer.py` - 规则配置管理脚本
- `config\test_case_rules.json` - 配置文件存储位置
- `mcp_tools\test_case_evaluator.py` - 评估器（已集成配置功能）

## 使用方法

### 1. 查看当前配置

```bash
uv run mcp_tools\test_case_rules_customer.py
```

输出示例：

```text
=== 当前测试用例评估规则配置 ===
标题最大长度: 40字符
步骤数上限: 10步
优先级占比:
  P0: 10-20%
  P1: 60-70%
  P2: 10-30%
最后更新: 2025-07-18T17:07:34.386099
配置文件位置: D:\...\config\test_case_rules.json
```

### 2. 交互式配置修改

```bash
uv run mcp_tools\test_case_rules_customer.py --config
```

交互过程：

```text
=== 测试用例评估规则配置 ===
当前配置项修改（直接回车保持当前值）：

标题最大长度 (当前: 40字符): 50
步骤数上限 (当前: 10步): 12

优先级占比配置（格式：最小值-最大值，如：10-20）：
P0占比% (当前: 10-20%): 15-25
P1占比% (当前: 60-70%): 
P2占比% (当前: 10-30%): 

新配置预览：
标题最大长度: 50字符
步骤数上限: 12步
优先级占比:
  P0: 15-25%
  P1: 60-70%
  P2: 10-30%

确认保存此配置？(y/n): y
配置保存成功！
```

### 3. 重置为默认配置

```bash
uv run mcp_tools\test_case_rules_customer.py --reset
```

### 4. 查看帮助信息

```bash
uv run mcp_tools\test_case_rules_customer.py --help
```

## 配置项说明

### 标题长度限制

- **默认值**: 40字符
- **有效范围**: 1-200字符
- **作用**: 控制测试用例标题的最大字符数
- **示例**: 设置为50字符，则提示词中会显示"标题长度不超过 50 字符"

### 步骤数上限

- **默认值**: 10步
- **有效范围**: 1-50步
- **作用**: 控制测试步骤的最大数量
- **示例**: 设置为15步，则提示词中会显示"不超过15步，否则需分解为多个用例"

### 优先级占比

- **P0默认值**: 10%-20%
- **P1默认值**: 60%-70%
- **P2默认值**: 10%-30%
- **有效范围**: 0%-100%，且最小值≤最大值
- **作用**: 控制各优先级在整体用例中的合理占比范围

## 集成使用

当运行 `test_case_evaluator.py` 时，评估器会自动：

1. **加载当前配置**

   ```python
   # 评估器启动时的日志
   已加载测试用例评估规则配置: 标题长度≤50字符, 步骤数≤12步
   ```

2. **构建动态提示词**
   - 自动将配置的数值插入到提示词模板中
   - 生成符合当前规则的评估标准

3. **应用新规则评估**
   - AI将按照新的规则标准进行评估
   - 评分和建议基于最新的配置要求

## 配置文件格式

`config\test_case_rules.json` 文件格式：

```json
{
  "title_max_length": 40,
  "max_steps": 10,
  "priority_ratios": {
    "P0": {"min": 10, "max": 20},
    "P1": {"min": 60, "max": 70},
    "P2": {"min": 10, "max": 30}
  },
  "version": "1.0",
  "last_updated": "2025-07-18T17:07:34.386099"
}
```

## 注意事项

1. **配置持久化**: 所有配置修改都会保存到本地文件，下次启动时自动加载
2. **配置验证**: 系统会验证配置的有效性，无效配置会回退到默认值
3. **实时生效**: 修改配置后，下次运行评估器时新配置立即生效
4. **备份建议**: 重要配置修改前建议备份配置文件

## 实际使用流程

1. **首次使用**：运行查看命令了解当前配置
2. **需要调整**：运行交互式配置命令修改参数
3. **运行评估**：正常运行 `test_case_evaluator.py`，新规则自动生效
4. **恢复默认**：如需要可随时重置为默认配置

这样就实现了灵活的规则配置管理，用户可以根据不同项目的需求调整评估标准。
